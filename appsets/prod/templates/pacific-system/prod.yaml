apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: pacific-system-prod
  namespace: argocd
  finalizers:
  - resources-finalizer.argocd.argoproj.io
spec:
  syncPolicy:
    applicationsSync: create-update
    preserveResourcesOnDeletion: true
  goTemplate: true
  goTemplateOptions: [ "missingkey=error" ]
  generators:
  - git:
      repoURL: https://git.styl.solutions/pacific-ii/infra/helm
      revision: master
      directories:
      - path: pacific-system/prod/*
  template:
    metadata:
      finalizers: []
      labels:
        app: '{{ `{{ printf "%s" (index .path.segments 2) }}` }}'
        cluster: '{{ `{{ printf "%s" (index .path.segments 1) }}` }}'
      name: '{{ `{{ printf "%s-%s" (index .path.segments 2) (index .path.segments 1) }}` }}'
    spec:
      destination:
        name: '{{ `{{ printf "%s" (index .path.segments 1) }}` }}'
        namespace: '{{ `{{ printf "%s" (index .path.segments 2) }}` }}'
      project: pacific-system
      source:
        helm:
          releaseName: '{{ `{{ printf "%s" (index .path.segments 2) }}` }}'
        path: '{{ `{{ printf "%s/%s/%s" (index .path.segments 0) (index .path.segments 1) (index .path.segments 2) }}` }}'
        repoURL: https://git.styl.solutions/pacific-ii/infra/helm
        targetRevision: master
      ignoreDifferences:
      - group: apps
        kind: Deployment
        jsonPointers:
        - /spec/replicas
      syncPolicy:
        applicationsSync: create-update
        preserveResourcesOnDeletion: true
        automated:
          prune: false
          selfHeal: false
        syncOptions:
        - CreateNamespace=true
        - RespectIgnoreDifferences=true
        - ServerSideApply=true
