apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-custom
data:
  application.conf: |
    [INPUT]
        Name                tail
        Tag                 application.*
        Exclude_Path        /var/log/containers/cloudwatch-agent*, /var/log/containers/fluent-bit*, /var/log/containers/aws-node*, /var/log/containers/kube-proxy*
        Path                /var/log/containers/*.json
        #DB                  /fluent-bit/state/flb_container.db
        Mem_Buf_Limit       50MB
        Skip_Long_Lines     On
        Refresh_Interval    10
        Rotate_Wait         30
        Read_from_Head      False
        Parser              docker-json

    [OUTPUT]
        Name                cloudwatch_logs
        Match               application.*
        region              ap-southeast-1
        log_group_name      /aws/containerinsights/pacific-ii-prod/application
        log_stream_prefix   microservices-
        auto_create_group   true
        extra_user_agent    container-insights

  debezium.conf: |
    [INPUT]
        Name                tail
        Tag                 debezium.*
        Path                /var/log/containers/debezium*
        #DB                  /var/fluent-bit/state/flb_debezium.db
        Mem_Buf_Limit       50MB
        Skip_Long_Lines     On
        Refresh_Interval    10
        Rotate_Wait         30
        Read_from_Head      False
        #Parser              docker-json

    [OUTPUT]
        Name                cloudwatch_logs
        Match               debezium.*
        region              ap-southeast-1
        log_group_name      /aws/containerinsights/pacific-ii-prod/debezium
        log_stream_prefix   microservices-
        extra_user_agent    container-insights
        log_key             log
  fluent-bit.conf: |
    [SERVICE]
        Flush                     5
        Log_Level                 info
        Daemon                    Off
        Parsers_File              /fluent-bit/etc/conf/parsers.conf
        HTTP_Server               On
        HTTP_Listen               0.0.0.0
        HTTP_Port                 2020
        Health_Check              On

    @INCLUDE /fluent-bit/etc/conf/karpenter.conf
    @INCLUDE /fluent-bit/etc/conf/application.conf
    @INCLUDE /fluent-bit/etc/conf/debezium.conf
    @INCLUDE /fluent-bit/etc/conf/schema-registry.conf
    @INCLUDE /fluent-bit/etc/conf/keycloak.conf
    @INCLUDE /fluent-bit/etc/conf/metabase.conf
  karpenter.conf: |
    [INPUT]
        Name                tail
        Tag                 karpenter.*
        #Exclude_Path        /var/log/containers/cloudwatch-agent*, /var/log/containers/fluent-bit*, /var/log/containers/aws-node*, /var/log/containers/kube-proxy*
        Path                /var/log/containers/karpenter*
        #DB                  /fluent-bit/etc/conf/flb_container.db
        Mem_Buf_Limit       50MB
        Skip_Long_Lines     On
        Refresh_Interval    10
        Rotate_Wait         30
        Read_from_Head      False
        Parser              docker-json

    [OUTPUT]
        Name                cloudwatch_logs
        Match               karpenter.*
        region              ap-southeast-1
        log_group_name      /aws/containerinsights/pacific-ii-prod/karpenter
        log_stream_prefix   karpenter-
        auto_create_group   true
  keycloak.conf: |
    [INPUT]
        Name                tail
        Tag                 keycloak.*
        Path                /var/log/containers/keycloak*
        #DB                  /var/fluent-bit/state/flb_keycloak.db
        Mem_Buf_Limit       50MB
        Skip_Long_Lines     On
        Refresh_Interval    10
        Rotate_Wait         30
        Read_from_Head      False
        #Parser              docker-json

    [OUTPUT]
        Name                cloudwatch_logs
        Match               keycloak.*
        region              ap-southeast-1
        log_group_name      /aws/containerinsights/pacific-ii-prod/application
        log_stream_prefix   microservices-
        extra_user_agent    container-insights
        log_key             log

  metabase.conf: |
    [OUTPUT]
      Name                cloudwatch_logs
      Match               metabase.*
      region              ap-southeast-1
      log_group_name      /aws/containerinsights/pacific-ii-prod/metabase
      log_stream_prefix   microservices-
      extra_user_agent    container-insights
      log_key             log
  parsers.conf: |
    [PARSER]
      Name   docker-json
      Format json
      Time_Key time
      Time_Format %Y-%m-%dT%H:%M:%S.%L
      Time_Keep On
      Decode_Field_As escaped_utf8 log
      Decode_Field_As escaped log
  schema-registry.conf: |
    [INPUT]
        Name                tail
        Tag                 schema-registry.*
        Path                /var/log/containers/pacific-registry*
        #DB                  /var/fluent-bit/state/flb_schema-registry.db
        Mem_Buf_Limit       50MB
        Skip_Long_Lines     On
        Refresh_Interval    10
        Rotate_Wait         30
        Read_from_Head      False
        #Parser              docker-json

    [OUTPUT]
        Name                cloudwatch_logs
        Match               schema-registry.*
        region              ap-southeast-1
        log_group_name      /aws/containerinsights/pacific-ii-prod/schema-registry
        log_stream_prefix   microservices-
        extra_user_agent    container-insights
        log_key             log
