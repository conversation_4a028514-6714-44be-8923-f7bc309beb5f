fluent-bit:
  affinity: {}
  annotations: {}
  args:
  - --workdir=/fluent-bit/etc
  - --config=/fluent-bit/etc/conf/fluent-bit.conf
  autoscaling:
    behavior: {}
    customRules: []
    enabled: false
    maxReplicas: 3
    minReplicas: 1
    targetCPUUtilizationPercentage: 75
    vpa:
      annotations: {}
      controlledResources: []
      enabled: false
      maxAllowed: {}
      minAllowed: {}
      updatePolicy:
        updateMode: Auto
  command:
  - /fluent-bit/bin/fluent-bit
  config:
    customParsers: |
      [PARSER]
          Name docker_no_time
          Format json
          Time_Keep Off
          Time_Key time
          Time_Format %Y-%m-%dT%H:%M:%S.%L
    extraFiles: {}
    filters: |
      [FILTER]
          Name kubernetes
          Match kube.*
          Merge_Log On
          Keep_Log Off
          K8S-Logging.Parser On
          K8S-Logging.Exclude On
    inputs: |
      [INPUT]
          Name tail
          Path /var/log/containers/*.log
          multiline.parser docker, cri
          Tag kube.*
          Mem_Buf_Limit 5MB
          Skip_Long_Lines On

      [INPUT]
          Name systemd
          Tag host.*
          Systemd_Filter _SYSTEMD_UNIT=kubelet.service
          Read_From_Tail On
    service: |
      [SERVICE]
          Daemon Off
          Flush {{ .Values.flush }}
          Log_Level {{ .Values.logLevel }}
          Parsers_File /fluent-bit/etc/parsers.conf
          #Parsers_File /fluent-bit/etc/conf/custom_parsers.conf
          HTTP_Server On
          HTTP_Listen 0.0.0.0
          HTTP_Port {{ .Values.metricsPort }}
          Health_Check On
    upstream: {}
  daemonSetVolumeMounts:
  - mountPath: /var/log
    name: varlog
  - mountPath: /var/lib/docker/containers
    name: varlibdockercontainers
    readOnly: true
  - mountPath: /etc/machine-id
    name: etcmachineid
    readOnly: true
  daemonSetVolumes:
  - hostPath:
      path: /var/log
    name: varlog
  - hostPath:
      path: /var/lib/docker/containers
    name: varlibdockercontainers
  - hostPath:
      path: /etc/machine-id
      type: File
    name: etcmachineid
  dashboards:
    annotations: {}
    deterministicUid: false
    enabled: false
    labelKey: grafana_dashboard
    labelValue: 1
    namespace: ""
  dnsConfig: {}
  dnsPolicy: ClusterFirst
  env: []
  envFrom: []
  envWithTpl: []
  existingConfigMap: fluent-bit-custom
  extraContainers: []
  extraPorts: []
  extraVolumeMounts: []
  extraVolumes: []
  flush: 5
  fullnameOverride: ""
  hostAliases: []
  hostNetwork: false
  hotReload:
    enabled: false
    extraWatchVolumes: []
    image:
      digest: null
      pullPolicy: IfNotPresent
      repository: ghcr.io/jimmidyson/configmap-reload
      tag: v0.14.0
    resources: {}
  image:
    digest: null
    pullPolicy: IfNotPresent
    tag: 3.2.6
  imagePullSecrets: []
  ingress:
    annotations: {}
    enabled: false
    extraHosts: []
    hosts: []
    ingressClassName: ""
    tls: []
  initContainers: []
  kind: DaemonSet
  labels: {}
  lifecycle: {}
  livenessProbe:
    httpGet:
      path: /
      port: http
  logLevel: info
  luaScripts: {}
  metricsPort: 2020
  minReadySeconds: null
  nameOverride: ""
  networkPolicy:
    enabled: false
  nodeSelector: {}
  openShift:
    enabled: false
    securityContextConstraints:
      annotations: {}
      create: true
      existingName: ""
      name: ""
      runAsUser:
        type: RunAsAny
      seLinuxContext:
        type: MustRunAs
  podAnnotations: {}
  podDisruptionBudget:
    annotations: {}
    enabled: false
    maxUnavailable: 30%
  podLabels: {}
  podSecurityContext: {}
  podSecurityPolicy:
    annotations: {}
    create: false
    runAsUser:
      rule: RunAsAny
    seLinux:
      rule: RunAsAny
  priorityClassName: ""
  prometheusRule:
    enabled: false
  rbac:
    create: true
    eventsAccess: false
    nodeAccess: false
  readinessProbe:
    httpGet:
      path: /api/v1/health
      port: http
  replicaCount: 1
  resources:
    limits:
      cpu: 500m
      memory: 1024Mi
    requests:
      cpu: 10m
      memory: 64Mi
  securityContext: {}
  service:
    annotations: {}
    externalIPs: []
    internalTrafficPolicy: null
    labels: {}
    loadBalancerClass: null
    loadBalancerSourceRanges: []
    port: 2020
    type: ClusterIP
  serviceAccount:
    annotations:
      eks.amazonaws.com/role-arn: arn:aws:iam::************:role/pacific-ii-prod-irsa-fluent-bit-cloudwatch
    create: true
    name: fluent-bit-sa
  serviceMonitor:
    additionalEndpoints: []
    enabled: false
  terminationGracePeriodSeconds: null
  testFramework:
    enabled: true
    image:
      digest: null
      pullPolicy: Always
      repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/devops/busybox
      tag: 1.31.1
    namespace: null
  tolerations: []
  updateStrategy: {}
  volumeMounts:
  - mountPath: /fluent-bit/etc/conf
    name: config
