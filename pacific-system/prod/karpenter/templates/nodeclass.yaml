# apiVersion: karpenter.k8s.aws/v1
# kind: EC2NodeClass
# metadata:
#   name: default
# spec:
#   amiFamily: AL2023
#   role: "KarpenterNodeRole-pacific-ii-prod"
#   subnetSelectorTerms:
#   - tags:
#       karpenter.sh/discovery: "pacific-ii-prod"
#   securityGroupSelectorTerms:
#   - tags:
#       karpenter.sh/discovery: "pacific-ii-prod"
#   amiSelectorTerms:
#   - id: "ami-02d2b1410be32d78b"
#   - id: "ami-0b7b6f823bee28feb"
