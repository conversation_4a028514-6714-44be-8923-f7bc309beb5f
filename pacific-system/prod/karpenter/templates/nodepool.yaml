# apiVersion: karpenter.sh/v1
# kind: NodePool
# metadata:
#   name: default
# spec:
#   template:
#     spec:
#       requirements:
#       - key: kubernetes.io/arch
#         operator: In
#         values: [ "amd64" ]
#       - key: kubernetes.io/os
#         operator: In
#         values: [ "linux" ]
#       - key: karpenter.sh/capacity-type
#         operator: In
#         values: [ "spot", "on-demand" ]
#       - key: karpenter.k8s.aws/instance-family
#         operator: In
#         values: [ "t2", "t3", "m5", "c5" ] # Added more instance families
#       - key: karpenter.k8s.aws/instance-category
#         operator: In
#         values: [ "t", "c", "m" ]
#       - key: karpenter.k8s.aws/instance-generation
#         operator: Gt
#         values: [ "2" ] # Allow older generation instances
#       - key: karpenter.k8s.aws/instance-size
#         operator: In
#         values: [ "medium", "large" ] # Allow larger instance sizes
#       nodeClassRef:
#         group: karpenter.k8s.aws
#         kind: EC2NodeClass
#         name: default
#       expireAfter: 720h
#   limits:
#     cpu: 6
#     memory: 12Gi
#   disruption:
#     consolidationPolicy: WhenEmptyOrUnderutilized
#     consolidateAfter: 1m
