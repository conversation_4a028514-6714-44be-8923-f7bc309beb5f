apiVersion: v2
appVersion: 0.7.2
description: Metrics Server is a scalable, efficient source of container resource metrics for Kubernetes built-in autoscaling pipelines.
home: https://github.com/kubernetes-sigs/metrics-server
icon: https://avatars.githubusercontent.com/u/36015203?s=400&v=4
name: metrics-server
type: application
version: 3.12.2
dependencies:
- name: metrics-server
  version: 3.12.2
  repository: https://kubernetes-sigs.github.io/metrics-server/
