aws-load-balancer-controller:
  replicaCount: 1

  revisionHistoryLimit: 10

  image:
    repository: public.ecr.aws/eks/aws-load-balancer-controller
    tag: v2.10.0
    pullPolicy: IfNotPresent

  runtimeClassName: ""
  imagePullSecrets: []
  nameOverride: ""
  fullnameOverride: ""

  # AWS LBC only has 1 main working pod, other pods are just standby
  # the purpose of enable hpa is to survive load induced failure by the calls to the aws-load-balancer-webhook-service
  # since the calls from kube-apiserver are sent round-robin to all replicas, and the failure policy on those webhooks is Fail
  # if the pods become overloaded and do not respond within the timeout that could block the creation of pods, targetgroupbindings or ingresses
  # Please keep in mind that the controller pods have `priorityClassName: system-cluster-critical`, enabling HPA may lead to the eviction of other low-priority pods in the node
  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 5
    targetCPUUtilizationPercentage: 80

  serviceAccount:
    # Specifies whether a service account should be created
    create: false
    # Annotations to add to the service account
    annotations:
      #eks.amazonaws.com/role-arn: 'arn:aws:iam::************:role/pacific-ii-prod-aws-load-balancer-controller'
      # The name of the service account to use.

    # If not set and create is true, a name is generated using the fullname template
    name: aws-load-balancer-controller
    # Automount API credentials for a Service Account.
    automountServiceAccountToken: true
    # List of image pull secrets to add to the Service Account.
    imagePullSecrets:
      # - name: docker

  rbac:
    # Specifies whether rbac resources should be created
    create: true

  podSecurityContext:
    fsGroup: 65534

  securityContext:
    # capabilities:
    #   drop:
    #   - ALL
    readOnlyRootFilesystem: true
    runAsNonRoot: true
    allowPrivilegeEscalation: false

  # Time period for the controller pod to do a graceful shutdown
  terminationGracePeriodSeconds: 10

  resources: {}
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    # limits:
    #   cpu: 100m
    #   memory: 128Mi
    # requests:
    #   cpu: 100m
    #   memory: 128Mi

  # priorityClassName specifies the PriorityClass to indicate the importance of controller pods
  # ref: https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass
  priorityClassName: system-cluster-critical

  nodeSelector: {}

  tolerations: []

  # affinity specifies a custom affinity for the controller pods
  affinity: {}

  # configureDefaultAffinity specifies whether to configure a default affinity for the controller pods to prevent
  # co-location on the same node. This will get ignored if you specify a custom affinity configuration.
  configureDefaultAffinity: true

  # topologySpreadConstraints is a stable feature of k8s v1.19 which provides the ability to
  # control how Pods are spread across your cluster among failure-domains such as regions, zones,
  # nodes, and other user-defined topology domains.
  #
  # more details here: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/
  topologySpreadConstraints: {}

  updateStrategy: {}
    # type: RollingUpdate
    # rollingUpdate:
    #   maxSurge: 1
    #   maxUnavailable: 1

  # serviceAnnotations contains annotations to be added to the provisioned webhook service resource
  serviceAnnotations: {}

  # deploymentAnnotations contains annotations for the controller deployment
  deploymentAnnotations: {}

  podAnnotations: {}

  podLabels: {}

  # additionalLabels -- Labels to add to each object of the chart.
  additionalLabels: {}

  # Enable cert-manager
  enableCertManager: false

  # The name of the Kubernetes cluster. A non-empty value is required
  clusterName: pacific-ii-prod

  # cluster contains configurations specific to the kubernetes cluster
  cluster:
    # Cluster DNS domain (required for requesting TLS certificates)
    dnsDomain: cluster.local

  # The ingress class this controller will satisfy. If not specified, controller will match all
  # ingresses without ingress class annotation and ingresses of type alb
  ingressClass: alb

  # ingressClassParams specify the IngressCLassParams that enforce settings for a set of Ingresses when using with ingress Controller.
  ingressClassParams:
    create: true
    # The name of ingressClassParams resource will be referred in ingressClass
    name:
    spec: {}
      # Due to dependency issue, the validation webhook ignores this particular ingressClassParams resource.
      # We recommend creating ingressClassParams resources separately after installing this chart and the
      # controller is functional.
      #
      # You can set the specifications in the `helm install` command through `--set` or `--set-string`
      # If you do want to specify in the values.yaml, uncomment the following
      # lines, adjust them as necessary, and remove the curly braces after 'spec:'
      #
      # eamespaceSelector:
      #   matchLabels:
      # group:
      # scheme:
      # ipAddressType:
      # tags:
      # loadBalancerAttributes:
      # - key:
      #   value:

  # To use IngressClass resource instead of annotation, before you need to install the IngressClass resource pointing to controller.
  # If specified as true, the IngressClass resource will be created.
  createIngressClassResource: true

  # The AWS region for the kubernetes cluster. Set to use KIAM or kube2iam for example.
  #region: ap-southeast-1

  # The VPC ID for the Kubernetes cluster. Set this manually when your pods are unable to use the metadata service to determine this automatically
  vpcId: 'vpc-00098afcee782317b'

  # Custom AWS API Endpoints (serviceID1=URL1,serviceID2=URL2)
  awsApiEndpoints:

  # awsApiThrottle specifies custom AWS API throttle settings (serviceID1:operationRegex1=rate:burst,serviceID2:operationRegex2=rate:burst)
  # example: --set awsApiThrottle="{Elastic Load Balancing v2:RegisterTargets|DeregisterTargets=4:20,Elastic Load Balancing v2:.*=10:40}"
  awsApiThrottle:

  # Maximum retries for AWS APIs (default 10)
  awsMaxRetries:

  # Default target type. Used as the default value of the "alb.ingress.kubernetes.io/target-type" and
  # "service.beta.kubernetes.io/aws-load-balancer-nlb-target-type" annotations.
  # Possible values are "ip" and "instance"
  # The value "ip" should be used for ENI-based CNIs, such as the Amazon VPC CNI,
  # Calico with encapsulation disabled, or Cilium with masquerading disabled.
  # The value "instance" should be used for overlay-based CNIs, such as Calico in VXLAN or IPIP mode or
  # Cilium with masquerading enabled.
  defaultTargetType: instance

  # If enabled, targetHealth readiness gate will get injected to the pod spec for the matching endpoint pods (default true)
  enablePodReadinessGateInject:

  # Enable Shield addon for ALB (default true)
  enableShield:

  # Enable WAF addon for ALB (default true)
  enableWaf:

  # Enable WAF V2 addon for ALB (default true)
  enableWafv2:

  # Maximum number of concurrently running reconcile loops for ingress (default 3)
  ingressMaxConcurrentReconciles:

  # Set the controller log level - info(default), debug (default "info")
  logLevel:

  # The address the metric endpoint binds to. (default ":8080")
  metricsBindAddr: ""

  webhookConfig:
    # disableIngressValidation disables the validation of resources of kind Ingress, false by default
    disableIngressValidation:

      # The TCP port the Webhook server binds to. (default 9443)
  webhookBindPort:

  # webhookTLS specifies TLS cert/key for the webhook
  webhookTLS:
    caCert:
    cert:
    key:

      # array of namespace selectors for the pod mutator webhook
  webhookNamespaceSelectors: # - key: elbv2.k8s.aws/pod-readiness-gate-inject

  #   operator: In
  #   values:
  #   - enabled

  # keepTLSSecret specifies whether to reuse existing TLS secret for chart upgrade
  keepTLSSecret: true

  # Maximum number of concurrently running reconcile loops for service (default 3)
  serviceMaxConcurrentReconciles:

  # Maximum number of concurrently running reconcile loops for targetGroupBinding
  targetgroupbindingMaxConcurrentReconciles:

  # Maximum duration of exponential backoff for targetGroupBinding reconcile failures
  targetgroupbindingMaxExponentialBackoffDelay:

  # Period at which the controller forces the repopulation of its local object stores. (default 10h0m0s)
  syncPeriod:

  # Namespace the controller watches for updates to Kubernetes objects, If empty, all namespaces are watched.
  watchNamespace:

  # disableIngressClassAnnotation disables the usage of kubernetes.io/ingress.class annotation, false by default
  disableIngressClassAnnotation:

  # disableIngressGroupNameAnnotation disables the usage of alb.ingress.kubernetes.io/group.name annotation, false by default
  disableIngressGroupNameAnnotation:

  # tolerateNonExistentBackendService permits rules which specify backend services that don't exist, true by default (When enabled, it will return 503 error if backend service not exist)
  tolerateNonExistentBackendService:

  # tolerateNonExistentBackendAction permits rules which specify backend actions that don't exist, true by default (When enabled, it will return 503 error if backend action not exist)
  tolerateNonExistentBackendAction:

  # defaultSSLPolicy specifies the default SSL policy to use for TLS/HTTPS listeners
  defaultSSLPolicy:

  # Liveness probe configuration for the controller
  livenessProbe:
    failureThreshold: 2
    httpGet:
      path: /healthz
      port: 61779
      scheme: HTTP
    initialDelaySeconds: 30
    timeoutSeconds: 10

  # readiness probe configuration for the controller
  readinessProbe:
    failureThreshold: 2
    httpGet:
      path: /readyz
      port: 61779
      scheme: HTTP
    successThreshold: 1
    initialDelaySeconds: 10
    timeoutSeconds: 10

  # Environment variables to set for aws-load-balancer-controller pod.
  # We strongly discourage programming access credentials in the controller environment. You should setup IRSA or
  # comparable solutions like kube2iam, kiam etc instead.
  env:
    # ENV_1: ""
    # ENV_2: ""

  # Use Environment variables credentials from Secret (aws-secret) for aws-load-balancer-controller pod similarly as The EBS CSI Driver does.
  # envSecretName: aws-secret

  # Use envFrom to set environment variables from a Secret or ConfigMap
  # envFrom:
  #   - secretRef:
  #       name: my-secret

  # Specifies if aws-load-balancer-controller should be started in hostNetwork mode.
  # This is required if using a custom CNI where the managed control plane nodes are unable to initiate
  # network connections to the pods, for example using Calico CNI plugin on EKS. This is not required or
  # recommended if using the Amazon VPC CNI plugin.
  hostNetwork: false

  # Specifies the dnsPolicy that should be used for pods in the deployment
  #
  # This may need to be used to be changed given certain conditions. For instance, if one uses the cilium CNI
  # with certain settings, one may need to set `hostNetwork: true` and webhooks won't work unless `dnsPolicy`
  # is set to `ClusterFirstWithHostNet`. See https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#pod-s-dns-policy
  dnsPolicy:

  # extraVolumeMounts are the additional volume mounts. This enables setting up IRSA on non-EKS Kubernetes cluster
  extraVolumeMounts:
    # - name: aws-iam-token
    #   mountPath: /var/run/secrets/eks.amazonaws.com/serviceaccount
    #   readOnly: true

  # extraVolumes for the extraVolumeMounts. Useful to mount a projected service account token for example.
  extraVolumes:
    # - name: aws-iam-token
    #   projected:
    #     defaultMode: 420
    #     sources:
    #     - serviceAccountToken:
    #         audience: sts.amazonaws.com
    #         expirationSeconds: 86400
    #         path: token

  # defaultTags are the tags to apply to all AWS resources managed by this controller
  defaultTags: {}
    # default_tag1: value1
    # default_tag2: value2

  # podDisruptionBudget specifies the disruption budget for the controller pods.
  # Disruption budget will be configured only when the replicaCount is greater than 1
  podDisruptionBudget: {}
  #  maxUnavailable: 1

  # externalManagedTags is the list of tag keys on AWS resources that will be managed externally
  externalManagedTags: []

  # enableEndpointSlices enables k8s EndpointSlices for IP targets instead of Endpoints (default false)
  enableEndpointSlices:

  # enableBackendSecurityGroup enables shared security group for backend traffic (default true)
  enableBackendSecurityGroup:

  # backendSecurityGroup specifies backend security group id (default controller auto create backend security group)
  backendSecurityGroup:

  # disableRestrictedSecurityGroupRules specifies whether to disable creating port-range restricted security group rules for traffic
  disableRestrictedSecurityGroupRules:

  # controllerConfig specifies controller configuration
  controllerConfig:
    # featureGates set of key: value pairs that describe AWS load balance controller features
    featureGates: {}
    # ListenerRulesTagging: true
    # WeightedTargetGroups: true
    # ServiceTypeLoadBalancerOnly: false
    # EndpointsFailOpen: true
    # EnableServiceController: true
    # EnableIPTargetType: true
    # SubnetsClusterTagCheck: true
    # NLBHealthCheckAdvancedConfig: true
    # ALBSingleSubnet: false

  certDiscovery:
    allowedCertificateAuthorityARNs: "" # empty means all CAs are in scope

  # objectSelector for webhook
  objectSelector:
    matchExpressions: # - key: <key>

    #   operator: <operator>
    #   values:
    #   - <value>
    matchLabels:
      #   key: value

  serviceMonitor:
    # Specifies whether a service monitor should be created
    enabled: false
    # Namespace to create the service monitor in
    namespace: # Labels to add to the service monitor

    additionalLabels: {}
    # Prometheus scrape interval
    interval: 1m
    # Prometheus scrape timeout
    scrapeTimeout: # Relabelings to apply to samples before ingestion

    relabelings: # Metric relabelings to apply to samples before ingestion

    metricRelabelings:

      # clusterSecretsPermissions lets you configure RBAC permissions for secret resources
      # Access to secrets resource is required only if you use the OIDC feature, and instead of
      # enabling access to all secrets, we recommend configuring namespaced role/rolebinding.
      # This option is for backwards compatibility only, and will potentially be deprecated in future.
  clusterSecretsPermissions:
    # allowAllSecrets allows the controller to access all secrets in the cluster.
    # This is to get backwards compatible behavior, but *NOT* recommended for security reasons
    allowAllSecrets: false

  # ingressClassConfig contains configurations specific to the ingress class
  ingressClassConfig:
    default: false

  # enableServiceMutatorWebhook allows you enable the webhook which makes this controller the default for all new services of type LoadBalancer
  enableServiceMutatorWebhook: true

  # serviceMutatorWebhook contains configurations specific to the service mutator webhook
  serviceMutatorWebhookConfig:
    # whether or not to fail the service creation if the webhook fails
    failurePolicy: Fail
    # limit webhook to only mutate services matching the objectSelector
    objectSelector:
      matchExpressions: []
      # - key: <key>
      #   operator: <operator>
      #   values:
      #   - <value>
      matchLabels: {} # key: value
    # which operations trigger the webhook
    operations:
    - CREATE # - UPDATE

  # serviceTargetENISGTags specifies AWS tags, in addition to the cluster tags, for finding the target ENI SG to which to add inbound rules from NLBs.
  serviceTargetENISGTags:

  # Specifies the class of load balancer to use for services. This affects how services are provisioned if type LoadBalancer is used (default service.k8s.aws/nlb)
  loadBalancerClass:

    # creator will disable helm default labels, so you can only add yours
    # creator: "me"
