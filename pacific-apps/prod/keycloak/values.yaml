pacific-chart:
  replicaCount: 1

  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/pacific-ii-keycloak
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: "26.1.1-8162d67f"

  imagePullSecrets: []
  nameOverride: "keycloak"
  fullnameOverride: "keycloak"

  serviceAccount:
    # Specifies whether a service account should be created
    create: true
    # Automatically mount a ServiceAccount's API credentials?
    automount: true
    # Annotations to add to the service account
    annotations: {}
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: "keycloak-sa"

  annotations:
    secret.reloader.stakater.com/reload: "pacificii-keycloak-secret"

  podAnnotations: {}
  podLabels: {}

  podSecurityContext: {} # fsGroup: 2000

  securityContext: {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000

  #args:
  # - health-enabled

  service:
    # type: NodePort 
    type: ClusterIP
    port: 8080
    targetPort: 8080
    protocol: TCP
    name: keycloak
    ## Additional port to define in the Service
    additionalPorts:
    - name: management
      port: 9000
      targetPort: 9000
      protocol: TCP

  serviceAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9000"
    prometheus.io/path: "/metrics"

  ## Additional port to define in the Service
  additionalPorts:
  - name: management
    containerPort: 9000
    protocol: TCP

  ingress:
    enabled: true
    className: "alb"
    annotations:
      alb.ingress.kubernetes.io/group.order: '1'
      alb.ingress.kubernetes.io/group.name: pacific-ii-prod
      alb.ingress.kubernetes.io/load-balancer-name: "pacific-ii-prod"
      alb.ingress.kubernetes.io/tags: environment=PROD, styl-project-name=pacific-ii
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-2-2021-06
      alb.ingress.kubernetes.io/success-codes: 200-399
      alb.ingress.kubernetes.io/healthcheck-path: "/realms/master"
      alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=60
#         arn:aws:acm:ap-southeast-1:************:certificate/ffffc872-be5d-419b-8f1f-4715a2cedb0a
      alb.ingress.kubernetes.io/certificate-arn: |
        arn:aws:acm:ap-southeast-1:************:certificate/05d8a835-d7f1-47ae-88d7-f1961039a5e3
      alb.ingress.kubernetes.io/conditions.keycloak-svc: >
        [{"field":"path-pattern","pathPatternConfig":{"values":["/js/*", "/realms/*", "/resources/*"]}}]

    hosts:
    - host: id.terabite.sg
      paths:
      - path: /robot.txt
        pathType: ImplementationSpecific
    # - host: keycloak-internal.terabite.sg
    #   paths:
    #   - path: /*
    #     pathType: ImplementationSpecific

    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local

  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    requests:
      cpu: 200m
      memory: 640Mi

  startupProbe:
    httpGet:
      path: /health/live
      port: 9000
    failureThreshold: 40
    periodSeconds: 15
    timeoutSeconds: 5
  livenessProbe:
    httpGet:
      path: /health/live
      port: 9000
    initialDelaySeconds: 30
    timeoutSeconds: 5
    periodSeconds: 10
  readinessProbe:
    httpGet:
      path: /health/ready
      port: 9000
    initialDelaySeconds: 30
    timeoutSeconds: 5
  periodSeconds: 10

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 3
    #targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # Additional volumes on the output Deployment definition.
  volumes: []
  # - name: foo
  #   secret:
  #     secretName: mysecret
  #     optional: false

  # Additional volumeMounts on the output Deployment definition.
  volumeMounts: []
  # - name: foo
  #   mountPath: "/etc/foo"
  #   readOnly: true
  configmap:
    enabled: false
    configData:
    defaultData:


  nodeSelector: {}

  tolerations: []

  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              app.kubernetes.io/name: keycloak
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: keycloak

  externalSecrets:
    enabled: true
    items:
    - name: pacificii-keycloak-secret
      secretStoreRef:
        name: aws-secretsmanager-store
        kind: ClusterSecretStore
      keys:
      - remoteRef:
          key: pacific-ii-prod-keycloak-sm
        secretKey: pacificii-keycloak-secret

  envFrom:
  - secretRef:
      name: pacificii-keycloak-secret
