apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: backoffice-ui-secret-manager
  namespace: application
spec:
  refreshInterval: 3m
  secretStoreRef:
    name: aws-secretsmanager-store
    kind: ClusterSecretStore
  target:
    name: pacificii-backoffice-ui-secret
    creationPolicy: Owner
    template:
      data:
        # env.js: |
        #   const env = {
        #     PACIFIC_AUTH_SERVER_URL: '{{ .PACIFIC_AUTH_SERVER_URL }}',
        #     PACIFIC_REDIRECT_URL: '{{ .PACIFIC_REDIRECT_URL }}',
        #     PACIFIC_LOGOUT_REDIRECT_URL: '{{ .PACIFIC_LOGOUT_REDIRECT_URL }}',
        #     PACIFIC_REALM: '{{ .PACIFIC_REALM }}',
        #     PACIFIC_CLIENT_ID: '{{ .PACIFIC_CLIENT_ID }}',
        #     PACIFIC_IDLE_TIME: {{ .PACIFIC_IDLE_TIME }},
        #     PACIFIC_TIMEOUT_TIME: {{ .PACIFIC_TIMEOUT_TIME }},
        #     PACIFIC_PING_TIME: {{ .PACIFIC_PING_TIME }},
        #   };
        env.js: |
          const env = {
            PACIFIC_AUTH_SERVER_URL: 'https://id.terabite.sg/',
            PACIFIC_REDIRECT_URL: '',
            PACIFIC_LOGOUT_REDIRECT_URL: 'https://bo.terabite.sg/portal',
            PACIFIC_REALM: 'pacific-backoffice',
            PACIFIC_CLIENT_ID: 'bo-portal',
            PACIFIC_IDLE_TIME: 600,
            PACIFIC_TIMEOUT_TIME: 300,
            PACIFIC_PING_TIME: 120,
          };
  data:
  - secretKey: PACIFIC_AUTH_SERVER_URL
    remoteRef:
      key: pacific-ii-prod-backoffice-ui-sm
      property: PACIFIC_AUTH_SERVER_URL
  - secretKey: PACIFIC_REDIRECT_URL
    remoteRef:
      key: pacific-ii-prod-backoffice-ui-sm
      property: PACIFIC_REDIRECT_URL
  - secretKey: PACIFIC_LOGOUT_REDIRECT_URL
    remoteRef:
      key: pacific-ii-prod-backoffice-ui-sm
      property: PACIFIC_LOGOUT_REDIRECT_URL
  - secretKey: PACIFIC_REALM
    remoteRef:
      key: pacific-ii-prod-backoffice-ui-sm
      property: PACIFIC_REALM
  - secretKey: PACIFIC_CLIENT_ID
    remoteRef:
      key: pacific-ii-prod-backoffice-ui-sm
      property: PACIFIC_CLIENT_ID
  - secretKey: PACIFIC_IDLE_TIME
    remoteRef:
      key: pacific-ii-prod-backoffice-ui-sm
      property: PACIFIC_IDLE_TIME
  - secretKey: PACIFIC_PING_TIME
    remoteRef:
      key: pacific-ii-prod-backoffice-ui-sm
      property: PACIFIC_PING_TIME
  - secretKey: PACIFIC_TIMEOUT_TIME
    remoteRef:
      key: pacific-ii-prod-backoffice-ui-sm
      property: PACIFIC_TIMEOUT_TIME
