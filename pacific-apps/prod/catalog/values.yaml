pacific-chart:
  replicaCount: 1

  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/pacific-ii-catalog
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: "1.3.0-d7725476"

  imagePullSecrets: []
  nameOverride: "catalog"
  fullnameOverride: "catalog"

  serviceAccount:
    # Specifies whether a service account should be created
    create: true
    # Automatically mount a ServiceAccount's API credentials?
    automount: true
    # Annotations to add to the service account
    annotations: {}
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: "catalog-sa"

  annotations:
    argocd.argoproj.io/sync-wave: "0"
    secret.reloader.stakater.com/reload: "pacificii-catalog-secret"

  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9203"
    prometheus.io/path: "/actuator/prometheus"

  podLabels: {}

  podSecurityContext: {} # fsGroup: 2000

  securityContext: {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000

  service:
    type: ClusterIP
    port: 9203
    targetPort: 9203
    protocol: TCP
    name: catalog

  serviceAnnotations: {}

  ingress:
    enabled: false
    className: ""
    annotations: {}
      # kubernetes.io/ingress.class: nginx
      # kubernetes.io/tls-acme: "true"
    hosts:
    - host: chart-example.local
      paths:
      - path: /
        pathType: ImplementationSpecific
    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local

  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    requests:
      cpu: 200m
      memory: 400Mi

  startupProbe:
    httpGet:
      path: /actuator/health/liveness
      port: 9203
    failureThreshold: 40
    periodSeconds: 15
    timeoutSeconds: 5
  livenessProbe:
    httpGet:
      path: /actuator/health/liveness
      port: 9203
    initialDelaySeconds: 30
    timeoutSeconds: 5
  readinessProbe:
    httpGet:
      path: /actuator/health/readiness
      port: 9203
    initialDelaySeconds: 30
    timeoutSeconds: 5
  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 3
    #targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # # Additional volumes on the output Deployment definition.
  # volumes: []
  # # - name: foo
  # #   secret:
  # #     secretName: mysecret
  # #     optional: false

  # # Additional volumeMounts on the output Deployment definition.
  # volumeMounts: []
  # # - name: foo
  # #   mountPath: "/etc/foo"
  # #   readOnly: true

  # Volume Mounts
  volumeMounts:
  - name: log-volume
    mountPath: "/var/log/containers/"
    readOnly: false
  # Volumes
  volumes:
  - name: log-volume
    hostPath:
      path: "/var/log/containers/"
      type: DirectoryOrCreate

  configmap:
    enabled: false
    configData:
    defaultData:


  nodeSelector: {}

  tolerations: []

  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              app.kubernetes.io/name: catalog
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: capacity-type
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: catalog
    # minDomains: 2  # Ensures both on-demand and spot have pods
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: catalog

  externalSecrets:
    enabled: true
    items:
    - name: pacificii-catalog-secret
      secretStoreRef:
        name: aws-secretsmanager-store
        kind: ClusterSecretStore
      keys:
      - remoteRef:
          key: pacific-ii-prod-catalog-sm
        secretKey: pacificii-catalog-secret

  envFrom:
  - secretRef:
      name: pacificii-catalog-secret

  env:
  - name: JAVA_OPTS
    value: "-Xms128m -Xmx768m"
  - name: SERVICE_NAME
    value: "catalog"

  command:
  - "/bin/sh"
  - "-c"

  args:
  - java $JAVA_OPTS -jar /app/$SERVICE_NAME.jar;
