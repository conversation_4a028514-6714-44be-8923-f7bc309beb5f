apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: customer-portal-secret-manager
  namespace: application
spec:
  refreshInterval: 3m
  secretStoreRef:
    name: aws-secretsmanager-store
    kind: ClusterSecretStore
  target:
    name: pacificii-customer-portal-secret
    creationPolicy: Owner
    template:
      data:
        # .env: |
        #   KEYCLOAK_URL={{ .KEYCLOAK_URL }}
        #   KEYCLOAK_CLIENT_ID={{ .KEYCLOAK_CLIENT_ID }}
        #   REDIRECT_URL={{ .REDIRECT_URL }}
        #   BASE_API_URL={{ .BASE_API_URL }}
        .env: |
          KEYCLOAK_URL=https://id.terabite.sg
          KEYCLOAK_CLIENT_ID=customer-portal
          REDIRECT_URL=./portal
          BASE_API_URL=./
  data:
  - secretKey: KEYCLOAK_URL
    remoteRef:
      key: pacific-ii-prod-customer-portal-sm
      property: KEYCLOAK_URL
  - secretKey: KEYCLOAK_CLIENT_ID
    remoteRef:
      key: pacific-ii-prod-customer-portal-sm
      property: KE<PERSON><PERSON><PERSON>K_CLIENT_ID
  - secretKey: REDIRECT_URL
    remoteRef:
      key: pacific-ii-prod-customer-portal-sm
      property: REDIRECT_URL
  - secretKey: BASE_API_URL
    remoteRef:
      key: pacific-ii-prod-customer-portal-sm
      property: BASE_API_URL
