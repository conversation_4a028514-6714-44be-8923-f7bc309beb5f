pacific-chart:
  replicaCount: 1

  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/pacific-ii-customer-portal
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: "1.3.0-d7725476"

  imagePullSecrets: []
  configmap:
    enabled: false

  nameOverride: "customer-portal"
  fullnameOverride: "customer-portal"

  serviceAccount:
    # Specifies whether a service account should be created
    create: true
    # Automatically mount a ServiceAccount's API credentials?
    automount: true
    # Annotations to add to the service account
    annotations: {}
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: "customer-portal-sa"

  annotations: {}
  podAnnotations: {}
  podLabels: {}

  podSecurityContext: {} # fsGroup: 2000

  securityContext: {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000

  service:
    type: ClusterIP
    targetPort: 80
    name: customer-portal
    protocol: TCP
    port: 9010

  ingress:
    enabled: true
    className: "alb"
    annotations:
      alb.ingress.kubernetes.io/group.order: '11'
      alb.ingress.kubernetes.io/group.name: pacific-ii-prod
      alb.ingress.kubernetes.io/load-balancer-name: "pacific-ii-prod"
      alb.ingress.kubernetes.io/tags: environment=PROD, styl-project-name=pacific-ii
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
      alb.ingress.kubernetes.io/healthcheck-path: "/portal"
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/ssl-policy: ELBSecurityPolicy-TLS13-1-2-2021-06
      alb.ingress.kubernetes.io/success-codes: 200-399
      alb.ingress.kubernetes.io/target-group-attributes: stickiness.enabled=true,stickiness.lb_cookie.duration_seconds=60
      alb.ingress.kubernetes.io/certificate-arn: |
        arn:aws:acm:ap-southeast-1:************:certificate/3603f475-294b-420a-b0d9-48c40480957b,
        arn:aws:acm:ap-southeast-1:************:certificate/f80f06cb-9d48-42f6-917c-cac6213cbac6,
        arn:aws:acm:ap-southeast-1:************:certificate/29197b0e-e71a-4d74-aa31-de64c214ea90,
        arn:aws:acm:ap-southeast-1:************:certificate/05d8a835-d7f1-47ae-88d7-f1961039a5e3,
        arn:aws:acm:ap-southeast-1:************:certificate/60e25cf9-8efa-4c08-b3af-82cd33531cc9
    hosts:
    - host: ""
      paths:
      - path: /*
        pathType: ImplementationSpecific
    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local

  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 10m
      memory: 32Mi

  livenessProbe:
    httpGet:
      path: /portal
      port: 80
  readinessProbe:
    httpGet:
      path: /portal
      port: 80

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 100
    #targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # # Additional volumes on the output Deployment definition.
  # volumes: 
  #   - name: pacific-backoffice-ui-volume-configuration
  #     configMap:
  #       name: backoffice-ui-config


  # # Additional volumeMounts on the output Deployment definition.
  # volumeMounts: 
  #   - name: pacific-backoffice-ui-volume-configuration
  #     mountPath: /app/env.js
  #     subPath: env.js

  # Additional volumes on the output Deployment definition.
  volumes:
  - name: pacific-customer-portal-volume-configuration
    secret:
      secretName: pacificii-customer-portal-secret
  # - name: log-volume
  #   mountPath: "/var/log/containers/"
  #   readOnly: false


  # Additional volumeMounts on the output Deployment definition.
  volumeMounts:
  - name: pacific-customer-portal-volume-configuration
    mountPath: /app/portal/assets/.env
    subPath: .env
  # - name: log-volume
  #   hostPath:
  #     path: "/var/log/containers/"
  #     type: DirectoryOrCreate

  nodeSelector: {}

  tolerations: []

  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              app.kubernetes.io/name: customer-portal
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: capacity-type
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: customer-portal
    # minDomains: 2  # Ensures both on-demand and spot have pods
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: customer-portal

  envFrom:
  - secretRef:
      name: pacificii-customer-portal-secret

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
