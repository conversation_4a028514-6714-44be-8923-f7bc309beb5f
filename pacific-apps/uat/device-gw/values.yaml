pacific-chart:
  replicaCount: 0

  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/pacific-ii-device-aggregator
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: ""

  imagePullSecrets: []
  nameOverride: "device-gw"
  fullnameOverride: "device-gw"

  serviceAccount:
    # Specifies whether a service account should be created
    create: true
    # Automatically mount a ServiceAccount's API credentials?
    automount: true
    # Annotations to add to the service account
    annotations: {}
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: "device-gw-sa"

  annotations:
    argocd.argoproj.io/sync-wave: "0"
    secret.reloader.stakater.com/reload: "pacificii-device-gw-secret"

  podAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9111"
    prometheus.io/path: "/actuator/prometheus"

  podLabels: {}

  podSecurityContext: {} # fsGroup: 2000

  securityContext: {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsaggregator: 1000

  service:
    type: ClusterIP
    port: 9111
    targetPort: 9111
    protocol: TCP
    name: "device-gw"

  serviceAnnotations: {}

  ingress:
    enabled: false
    className: ""
    annotations: {}
      # kubernetes.io/ingress.class: nginx
      # kubernetes.io/tls-acme: "true"
    hosts:
    - host: chart-example.local
      paths:
      - path: /
        pathType: ImplementationSpecific
    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local

  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the aggregator. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    requests:
      cpu: 200m
      memory: 400Mi

  startupProbe:
    httpGet:
      path: /actuator/health/liveness
      port: 9111
    failureThreshold: 40
    periodSeconds: 15
    timeoutSeconds: 5
  livenessProbe:
    httpGet:
      path: /actuator/health/liveness
      port: 9111
    initialDelaySeconds: 30
    timeoutSeconds: 5
  readinessProbe:
    httpGet:
      path: /actuator/health/readiness
      port: 9111
    initialDelaySeconds: 30
    timeoutSeconds: 5
  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 3
    #targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # # Additional volumes on the output Deployment definition.
  # volumes: []
  # # - name: foo
  # #   secret:
  # #     secretName: mysecret
  # #     optional: false

  # # Additional volumeMounts on the output Deployment definition.
  # volumeMounts: []
  # # - name: foo
  # #   mountPath: "/etc/foo"
  # #   readOnly: true

  # Volume Mounts
  volumeMounts:
  - name: log-volume
    mountPath: "/var/log/containers/"
    readOnly: false
  # Volumes
  volumes:
  - name: log-volume
    hostPath:
      path: "/var/log/containers/"
      type: DirectoryOrCreate

  configmap:
    enabled: false
    configData:
    defaultData:


  nodeSelector: {}

  tolerations: []

  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              app.kubernetes.io/name: device-gw
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: capacity-type
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: device-gw
    # minDomains: 2  # Ensures both on-demand and spot have pods
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: DoNotSchedule
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: device-gw

  externalSecrets:
    enabled: true
    items:
    - name: pacificii-device-gw-secret
      secretStoreRef:
        name: aws-secretsmanager-store
        kind: ClusterSecretStore
      keys:
      - remoteRef:
          key: pacific-ii-uat-device-gw-sm
        secretKey: pacificii-device-gw-secret

  envFrom:
  - secretRef:
      name: pacificii-device-gw-secret

  env:
  - name: JAVA_OPTS
    value: "-Xms128m -Xmx1024m"
  - name: SERVICE_NAME
    value: "aggregator"

  command:
  - "/bin/sh"
  - "-c"

  args:
  - java $JAVA_OPTS -jar /app/$SERVICE_NAME.jar;
