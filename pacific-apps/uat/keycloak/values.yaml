pacific-chart:
  replicaCount: null

  image:
    repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/pacific-ii-keycloak
    pullPolicy: IfNotPresent
    # Overrides the image tag whose default is the chart appVersion.
    tag: "26.1.1-8162d67f"

  imagePullSecrets: []
  nameOverride: "keycloak"
  fullnameOverride: "keycloak"

  serviceAccount:
    # Specifies whether a service account should be created
    create: true
    # Automatically mount a ServiceAccount's API credentials?
    automount: true
    # Annotations to add to the service account
    annotations: {}
    # The name of the service account to use.
    # If not set and create is true, a name is generated using the fullname template
    name: "keycloak-sa"

  annotations:
    secret.reloader.stakater.com/reload: "pacificii-keycloak-secret"

  podAnnotations: {}
  podLabels: {}

  podSecurityContext: {} # fsGroup: 2000

  securityContext: {}
    # capabilities:
    #   drop:
    #   - ALL
    # readOnlyRootFilesystem: true
    # runAsNonRoot: true
    # runAsUser: 1000

  #args:
  # - health-enabled

  service:
    # type: NodePort 
    type: ClusterIP
    port: 8080
    targetPort: 8080
    protocol: TCP
    name: keycloak
    ## Additional port to define in the Service
    additionalPorts:
    - name: management
      port: 9000
      targetPort: 9000
      protocol: TCP

  serviceAnnotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9000"
    prometheus.io/path: "/metrics"

  ## Additional port to define in the Service
  additionalPorts:
  - name: management
    containerPort: 9000
    protocol: TCP

  ingress:
    enabled: true
    className: "alb"
    annotations:
      alb.ingress.kubernetes.io/scheme: internet-facing # Make ALB publicly accessible
      alb.ingress.kubernetes.io/certificate-arn: "" # Your ACM certificate ARN
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP":80}]' # ALB listens on port 80
      alb.ingress.kubernetes.io/target-type: instance # Targeting EKS worker nodes
      alb.ingress.kubernetes.io/backend-protocol: HTTP
    hosts:
      #- host: keycloak.local
      #  paths:
      #    - path: /
      #      pathType: Prefix

    tls: []
    #  - secretName: chart-example-tls
    #    hosts:
    #      - chart-example.local

  resources:
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    requests:
      cpu: 200m
      memory: 640Mi

  startupProbe:
    httpGet:
      path: /health/live
      port: 9000
    failureThreshold: 40
    periodSeconds: 15
    timeoutSeconds: 5
  livenessProbe:
    httpGet:
      path: /health/live
      port: 9000
    initialDelaySeconds: 30
    timeoutSeconds: 5
    periodSeconds: 10
  readinessProbe:
    httpGet:
      path: /health/ready
      port: 9000
    initialDelaySeconds: 30
    timeoutSeconds: 5
  periodSeconds: 10

  autoscaling:
    enabled: false
    minReplicas: 1
    maxReplicas: 3
    #targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80

  # Additional volumes on the output Deployment definition.
  volumes: []
  # - name: foo
  #   secret:
  #     secretName: mysecret
  #     optional: false

  # Additional volumeMounts on the output Deployment definition.
  volumeMounts: []
  # - name: foo
  #   mountPath: "/etc/foo"
  #   readOnly: true
  configmap:
    enabled: false
    configData:
    defaultData:


  nodeSelector: {}

  tolerations: []

  affinity:
    podAntiAffinity:
      preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchLabels:
              app.kubernetes.io/name: keycloak
          topologyKey: kubernetes.io/hostname

  topologySpreadConstraints:
  - maxSkew: 1
    topologyKey: kubernetes.io/hostname
    whenUnsatisfiable: ScheduleAnyway
    labelSelector:
      matchLabels:
        app.kubernetes.io/name: keycloak

  externalSecrets:
    enabled: true
    items:
    - name: pacificii-keycloak-secret
      secretStoreRef:
        name: aws-secretsmanager-store
        kind: ClusterSecretStore
      keys:
      - remoteRef:
          key: pacific-ii-uat-keycloak-sm
        secretKey: pacificii-keycloak-secret

  envFrom:
  - secretRef:
      name: pacificii-keycloak-secret
